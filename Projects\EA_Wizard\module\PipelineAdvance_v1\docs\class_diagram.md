# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 📋 完整模組列表與方法註解

### 🔧 核心介面模組 (Interface)

#### 1. ITradingPipeline 介面

**檔案**: `interface/ITradingPipeline.mqh`
**職責**: 定義所有交易流水線必須實現的基本功能

**方法列表**:

- `void Execute()` - 執行流水線主要邏輯，實現類應該在此方法中完成具體的業務處理
- `string GetName()` - 獲取流水線名稱，返回流水線的唯一標識名稱，用於識別和管理
- `string GetType()` - 獲取流水線類型，返回流水線的類型標識，用於分類和組織
- `bool IsExecuted()` - 檢查是否已執行，返回流水線的執行狀態，用於避免重複執行和狀態管理
- `void Restore()` - 重置流水線狀態，將流水線重置為未執行狀態，允許重新執行

#### 2. ITradingPipelineDriver 介面

**檔案**: `interface/ITradingPipelineDriver.mqh`
**職責**: 定義流水線驅動器必須實現的核心管理和控制功能

**方法列表**:

- `bool Initialize()` - 初始化驅動器，初始化所有核心組件（管理器、註冊器、探索器）
- `TradingPipelineContainerManager* GetManager() const` - 獲取容器管理器，返回流水線容器管理器實例，用於管理所有容器
- `TradingPipelineRegistry* GetRegistry() const` - 獲取註冊器，返回流水線註冊器實例，用於註冊和管理流水線
- `TradingPipelineExplorer* GetExplorer() const` - 獲取探索器，返回流水線探索器實例，用於查詢和探索流水線
- `ObjectRegistry* GetObjectRegistry() const` - 獲取對象註冊器，返回對象註冊器實例，用於註冊和管理各種對象
- `TradingMessageHandler* GetMessageHandler() const` - 獲取訊息處理器，返回訊息處理器實例，用於處理和管理系統訊息
- `bool IsInitialized() const` - 檢查初始化狀態，返回驅動器是否已成功初始化
- `string GetName() const` - 獲取驅動器名稱，返回驅動器的唯一標識名稱
- `string GetType() const` - 獲取驅動器類型，返回驅動器的類型標識
- `bool SetupDefaultConfiguration()` - 設置默認配置，創建和配置默認的流水線容器和組件
- `void Cleanup()` - 清理驅動器，清理所有組件和資源，準備驅動器關閉

### 🏗️ 核心抽象基類模組

#### 3. TradingPipeline 抽象基類

**檔案**: `TradingPipeline.mqh`
**職責**: 提供流水線的基本實現和模板方法，實現 ITradingPipeline 介面

**方法列表**:

- `TradingPipeline(string name, string type, ENUM_TRADING_STAGE stage, ITradingPipelineDriver* driver)` - 構造函數，初始化流水線基本屬性
- `virtual ~TradingPipeline()` - 析構函數，清理資源
- `virtual void Execute()` - 執行流水線，實現模板方法模式，調用子類的 Main() 方法
- `string GetName()` - 獲取流水線名稱
- `string GetType()` - 獲取流水線類型
- `bool IsExecuted()` - 檢查是否已執行
- `void Restore()` - 重置流水線狀態，將執行狀態重置為未執行
- `ENUM_TRADING_STAGE GetStage()` - 獲取交易階段，返回流水線所屬的交易階段
- `ITradingPipelineDriver* GetDriver()` - 獲取驅動器實例
- `PipelineResult* GetResult()` - 獲取執行結果，返回最後一次執行的結果
- `virtual void Main() = 0` - 抽象方法，子類必須實現的主要業務邏輯

#### 4. TradingPipelineContainerBase 抽象基類

**檔案**: `TradingPipelineContainerBase.mqh`
**職責**: 提供容器管理的基本功能和模板方法模式，實現 ITradingPipeline 介面

**方法列表**:

- `TradingPipelineContainerBase(string name, string type, string description, bool owned, int maxPipelines)` - 構造函數，初始化容器基本屬性
- `virtual ~TradingPipelineContainerBase()` - 析構函數，清理資源
- `bool AddPipeline(ITradingPipeline* pipeline)` - 添加子流水線到容器
- `bool RemovePipeline(ITradingPipeline* pipeline)` - 從容器移除子流水線
- `void Clear()` - 清空所有子流水線
- `int GetPipelineCount() const` - 獲取子流水線數量
- `int GetMaxPipelines() const` - 獲取最大容量
- `bool HasPipeline(ITradingPipeline* pipeline) const` - 檢查是否包含指定流水線
- `virtual void Execute() override` - 執行流水線（模板方法）
- `virtual string GetName() override` - 獲取容器名稱
- `virtual string GetType() override` - 獲取容器類型
- `virtual bool IsExecuted() override` - 檢查是否已執行
- `virtual void Restore() override` - 重置執行狀態
- `virtual bool PreExecuteCheck()` - 執行前置檢查（鉤子方法）
- `virtual void ExecuteInternal() = 0` - 執行具體邏輯（抽象方法）
- `virtual void PostExecuteProcess()` - 執行後置處理（鉤子方法）

#### 5. TradingPipelineDriverBase 抽象基類

**檔案**: `TradingPipelineDriverBase.mqh`
**職責**: 提供驅動器管理的基本功能和模板方法模式，實現 ITradingPipelineDriver 介面

**方法列表**:

- `TradingPipelineDriverBase(string name, string type)` - 構造函數，初始化驅動器基本屬性
- `virtual ~TradingPipelineDriverBase()` - 析構函數，清理所有組件
- `virtual bool Initialize() override` - 初始化驅動器（模板方法）
- `virtual TradingPipelineContainerManager* GetManager() const override` - 獲取容器管理器
- `virtual TradingPipelineRegistry* GetRegistry() const override` - 獲取註冊器
- `virtual TradingPipelineExplorer* GetExplorer() const override` - 獲取探索器
- `virtual ObjectRegistry* GetObjectRegistry() const override` - 獲取對象註冊器
- `virtual TradingMessageHandler* GetMessageHandler() const override` - 獲取訊息處理器
- `virtual bool IsInitialized() const override` - 檢查初始化狀態
- `virtual string GetName() const override` - 獲取驅動器名稱
- `virtual string GetType() const override` - 獲取驅動器類型
- `virtual bool SetupDefaultConfiguration() override` - 設置默認配置
- `virtual void Cleanup() override` - 清理驅動器資源
- `virtual bool PreInitializeCheck()` - 初始化前置檢查（鉤子方法）
- `virtual bool InitializeComponents() = 0` - 初始化核心組件（抽象方法）
- `virtual bool SetupConfiguration() = 0` - 設置配置（抽象方法）
- `virtual bool PostInitializeProcess()` - 初始化後置處理（鉤子方法）

#### 6. MainPipeline 抽象基類

**檔案**: `MainPipeline.mqh`
**職責**: 繼承 TradingPipeline，提供主流水線的基本實現，自動註冊到驅動器，並提供對象管理和錯誤處理功能

**方法列表**:

- `MainPipeline(ENUM_TRADING_STAGE stage, string name, string type, ITradingPipelineDriver* driver)` - 構造函數，自動將流水線註冊到驅動器的註冊器中，並處理註冊結果
- `virtual void Main() = 0` - 抽象方法，子類必須實現的主程序邏輯
- `TradingMessageHandler* GetMessageHandler()` - 獲取錯誤處理器，返回驅動器的訊息處理器實例
- `ObjectDetail* GetObjectDetail(string name)` - 獲取對象詳細信息，從驅動器的對象註冊器中查詢指定對象
- `bool Register(void* object, string name, string description)` - 註冊對象到 ObjectRegistry，將對象註冊到驅動器的對象註冊器中
- `bool Unregister(string name)` - 從 ObjectRegistry 移除對象，從驅動器的對象註冊器中移除指定對象

**宏定義**:

- `PipelineObjectIsRegistered(ObjectType, name)` - 檢查指定類型的對象是否已註冊
- `GetPipelineObject(ObjectType, name)` - 獲取指定類型的已註冊對象

### 📦 容器管理模組

#### 7. TradingPipelineContainer 容器類

**檔案**: `TradingPipelineContainer.mqh`
**職責**: 統一容器類，實現組合模式，可包含多個子流水線

**方法列表**:

- `TradingPipelineContainer(string name, string description, string type, ENUM_TRADING_EVENT eventType, bool owned, int maxPipelines)` - 構造函數，初始化容器屬性
- `virtual ~TradingPipelineContainer()` - 析構函數，清理所有子流水線
- `bool AddPipeline(ITradingPipeline* pipeline)` - 添加流水線，將子流水線添加到容器中
- `bool RemovePipeline(ITradingPipeline* pipeline)` - 移除流水線，從容器中移除指定的子流水線
- `bool RemovePipelineByName(string name)` - 按名稱移除流水線
- `ITradingPipeline* FindByName(string name, ITradingPipeline* defaultValue)` - 按名稱查找流水線
- `ITradingPipeline* GetPipeline(int index, ITradingPipeline* defaultValue)` - 按索引獲取流水線
- `void Clear()` - 清空所有子流水線
- `int GetPipelineCount()` - 獲取子流水線數量
- `int GetMaxPipelines()` - 獲取最大容量
- `bool HasPipeline(ITradingPipeline* pipeline)` - 檢查是否包含指定流水線
- `bool HasPipelineByName(string name)` - 按名稱檢查是否包含流水線
- `int GetAllPipelines(ITradingPipeline* &pipelines[])` - 獲取所有子流水線
- `int AddPipelines(ITradingPipeline* &pipelines[])` - 批量添加流水線
- `bool AreAllPipelinesExecuted()` - 檢查所有子流水線是否都已執行
- `void Execute()` - 執行容器，依序執行所有子流水線
- `string GetName()` - 獲取容器名稱
- `string GetType()` - 獲取容器類型
- `bool IsExecuted()` - 檢查容器是否已執行
- `void Restore()` - 重置容器狀態，重置所有子流水線的執行狀態
- `ENUM_TRADING_EVENT GetEventType()` - 獲取事件類型
- `bool IsEnabled()` - 檢查容器是否啟用
- `void SetEnabled(bool enabled)` - 設置容器啟用狀態
- `bool IsFull()` - 檢查容器是否已滿
- `bool IsEmpty()` - 檢查容器是否為空
- `string GetStatusInfo()` - 獲取詳細狀態信息

#### 8. TradingPipelineContainerManager 容器管理器

**檔案**: `TradingPipelineContainerManager.mqh`
**職責**: 管理多個流水線容器，提供統一的執行和管理介面

**方法列表**:

- `TradingPipelineContainerManager(string name, string type, bool owned, int maxContainers)` - 構造函數，初始化管理器
- `virtual ~TradingPipelineContainerManager()` - 析構函數，清理所有容器
- `bool AddContainer(TradingPipelineContainer* container)` - 添加容器到管理器
- `bool RemoveContainer(TradingPipelineContainer* container)` - 從管理器移除容器
- `bool RemoveContainerByName(string name)` - 按名稱移除容器
- `TradingPipelineContainer* FindContainerByName(string name)` - 按名稱查找容器
- `TradingPipelineContainer* FindContainerByEventType(ENUM_TRADING_EVENT eventType)` - 按事件類型查找容器
- `int GetContainersByEventType(ENUM_TRADING_EVENT eventType, TradingPipelineContainer* &containers[])` - 獲取指定事件類型的所有容器
- `TradingPipelineContainer* GetContainer(int index)` - 按索引獲取容器
- `int GetAllContainers(TradingPipelineContainer* &containers[])` - 獲取所有容器
- `void Clear()` - 清空所有容器
- `int GetContainerCount()` - 獲取容器數量
- `int GetMaxContainers()` - 獲取最大容器數量
- `bool Execute(ENUM_TRADING_EVENT eventType)` - 執行指定事件類型的容器，返回執行是否成功
- `void ExecuteAll()` - 執行所有容器
- `void Restore(ENUM_TRADING_EVENT eventType)` - 重置指定事件類型的容器
- `void RestoreAll()` - 重置所有容器
- `string GetName()` - 獲取管理器名稱
- `string GetType()` - 獲取管理器類型
- `void SetEnabled(bool enabled)` - 設置管理器啟用狀態
- `bool IsEnabled()` - 檢查管理器是否啟用
- `bool HasEmptySlot()` - 檢查是否有空閒槽位
- `bool IsEmpty()` - 檢查管理器是否為空
- `bool IsFull()` - 檢查管理器是否已滿
- `string GetStatusInfo()` - 獲取詳細狀態信息
- `void EnableContainersByEventType(ENUM_TRADING_EVENT eventType, bool enabled)` - 按事件類型啟用/禁用容器
- `int GetContainerCountByEventType(ENUM_TRADING_EVENT eventType)` - 獲取指定事件類型的容器數量

### 🚗 驅動器模組

#### 9. TradingPipelineDriver 驅動器類 (單例)

**檔案**: `TradingPipelineDriver.mqh`
**職責**: 系統核心控制器，單例模式，統一管理整個流水線系統

**方法列表**:

- `static TradingPipelineDriver* GetInstance()` - 獲取單例實例，確保全局唯一的驅動器實例
- `bool Initialize()` - 初始化驅動器，初始化所有核心組件
- `TradingPipelineContainerManager* GetManager() const` - 獲取容器管理器
- `TradingPipelineRegistry* GetRegistry() const` - 獲取註冊器
- `TradingPipelineExplorer* GetExplorer() const` - 獲取探索器
- `ObjectRegistry* GetObjectRegistry() const` - 獲取對象註冊器
- `TradingMessageHandler* GetMessageHandler() const` - 獲取訊息處理器
- `bool IsInitialized() const` - 檢查初始化狀態
- `string GetName() const` - 獲取驅動器名稱
- `string GetType() const` - 獲取驅動器類型
- `bool SetupDefaultConfiguration()` - 設置默認配置，創建所有預設階段容器（包括初始化、交易執行、清理等各階段容器）
- `bool RegisterErrorHandlerToObjectRegistry()` - 註冊錯誤處理器到對象註冊器，將 TradingMessageHandler 註冊到 ObjectRegistry 中
- `void Cleanup()` - 清理驅動器資源

### 📋 註冊管理模組

#### 10. TradingPipelineRegistry 註冊器類

**檔案**: `TradingPipelineRegistry.mqh`
**職責**: 管理流水線註冊，按階段和事件類型組織流水線

**方法列表**:

- `TradingPipelineRegistry(string name, string type)` - 構造函數，初始化註冊器
- `virtual ~TradingPipelineRegistry()` - 析構函數，清理註冊的流水線
- `bool Register(ITradingPipeline* pipeline)` - 註冊流水線，將流水線註冊到對應的階段容器中
- `bool Unregister(ITradingPipeline* pipeline)` - 取消註冊流水線
- `bool IsStageRegistered(ENUM_TRADING_STAGE stage)` - 檢查階段是否已註冊
- `bool IsEventRegistered(ENUM_TRADING_EVENT event)` - 檢查事件是否已註冊
- `TradingPipelineContainer* GetRegisteredStageContainer(ENUM_TRADING_STAGE stage)` - 獲取已註冊的階段容器
- `TradingPipelineContainer* GetRegisteredEventContainer(ENUM_TRADING_EVENT event)` - 獲取已註冊的事件容器
- `int GetRegisteredStageCount()` - 獲取已註冊的階段數量
- `int GetRegisteredEventCount()` - 獲取已註冊的事件數量
- `string GetName()` - 獲取註冊器名稱
- `string GetType()` - 獲取註冊器類型
- `void Clear()` - 清空所有註冊

#### 11. TradingPipelineExplorer 探索器類

**檔案**: `TradingPipelineExplorer.mqh`
**職責**: 查詢和探索流水線，提供靈活的搜索和報告功能

**方法列表**:

- `TradingPipelineExplorer(TradingPipelineRegistry* registry)` - 構造函數，關聯註冊器
- `virtual ~TradingPipelineExplorer()` - 析構函數
- `ITradingPipeline* GetPipeline(ENUM_TRADING_STAGE stage)` - 根據階段獲取流水線
- `ITradingPipeline* GetPipeline(ENUM_TRADING_EVENT event)` - 根據事件獲取流水線
- `int GetAllPipelinesByStage(ENUM_TRADING_STAGE stage, ITradingPipeline* &pipelines[])` - 獲取指定階段的所有流水線
- `int GetAllPipelinesByEvent(ENUM_TRADING_EVENT event, ITradingPipeline* &pipelines[])` - 獲取指定事件的所有流水線
- `bool HasPipelineForStage(ENUM_TRADING_STAGE stage)` - 檢查是否有指定階段的流水線
- `bool HasPipelineForEvent(ENUM_TRADING_EVENT event)` - 檢查是否有指定事件的流水線
- `int GetPipelineCountByEvent(ENUM_TRADING_EVENT event)` - 獲取指定事件的流水線數量
- `int GetPipelineCountByStage(ENUM_TRADING_STAGE stage)` - 獲取指定階段的流水線數量
- `int GetTotalPipelineCount()` - 獲取總流水線數量
- `string GenerateExplorationReport()` - 生成探索報告，提供系統概覽
- `string GenerateStageReport()` - 生成階段報告
- `string GenerateEventReport()` - 生成事件報告

### 🎮 控制器模組

#### 12. TradingController EA 生命週期控制器

**檔案**: `TradingController.mqh`
**職責**: EA 生命週期的統一入口點，協調 OnInit、OnTick、OnDeinit 方法

**方法列表**:

- `TradingController(TradingPipelineDriver* driver, string name)` - 構造函數，接收驅動器實例
- `virtual ~TradingController()` - 析構函數，清理資源
- `ENUM_INIT_RETCODE OnInit()` - EA 初始化方法，執行初始化事件的流水線
- `void OnTick()` - EA 交易執行方法，在每個 tick 時執行交易流水線
- `void OnDeinit(int reason)` - EA 清理方法，執行清理事件的流水線
- `bool IsInitialized()` - 檢查控制器是否已初始化
- `string GetName()` - 獲取控制器名稱
- `string GetType()` - 獲取控制器類型
- `PipelineResult* GetLastResult()` - 獲取最後執行結果
- `bool ExecuteInitPipeline()` - 執行初始化流水線，直接返回管理器執行結果
- `bool ExecuteTickPipeline()` - 執行交易流水線，包含重置和執行邏輯，直接返回管理器執行結果
- `bool ExecuteDeinitPipeline(int reason)` - 執行清理流水線，直接返回管理器執行結果

### ⚠️ 訊息處理模組

#### 13. TradingMessageHandler 訊息處理器

**檔案**: `TradingMessageHandler.mqh`
**職責**: 統一訊息處理器，實現訪問者模式，支持靈活的訊息處理策略，提供內存安全的訊息管理

**核心特性**:

- ✅ **內存安全**: 所有返回的記錄都是副本，避免無效指針訪問
- ✅ **訪問者模式**: 支持靈活的訊息處理策略
- ✅ **級別管理**: 支持 INFO、WARNING、ERROR、CRITICAL 四個級別
- ✅ **容量控制**: 可配置最大訊息數量，自動清理舊記錄
- ✅ **統計功能**: 提供各級別訊息統計和摘要

**核心方法**:

- `TradingMessageHandler()` - 構造函數，初始化訊息處理器（默認最大 1000 條訊息）
- `virtual ~TradingMessageHandler()` - 析構函數，安全清理所有訊息記錄
- `void SetMaxMessageCount(int maxMsg)` - 設置最大訊息數量，控制訊息記錄的容量
- `void AddMessage(string message, string source, ENUM_ERROR_LEVEL level)` - 添加訊息記錄，無輸出版本
- `void HandleMessage(TradingMessageVisitor* visitor)` - 處理訊息，使用訪問者模式處理所有訊息記錄
- `void HandlePipelineResult(PipelineResult* result)` - 處理 PipelineResult 訊息
- `void HandlePipelineResults(PipelineResult* &results[], int count)` - 批量處理 PipelineResult 陣列
- `void ClearMessages()` - 清除所有訊息記錄

**查詢方法（返回副本，需要手動刪除）**:

- `TradingMessageRecord* GetLastMessage(ENUM_ERROR_LEVEL level) const` - 獲取指定級別的最後一個訊息記錄副本
- `TradingMessageRecord* GetFirstMessage(ENUM_ERROR_LEVEL level) const` - 獲取指定級別的第一個訊息記錄副本
- `TradingMessageRecord* GetMessageRecord(int index) const` - 獲取指定索引的訊息記錄副本
- `TradingMessageRecord* PopMessage(ENUM_ERROR_LEVEL level)` - 彈出最後一個訊息（移除並返回副本）
- `TradingMessageRecord* ShiftMessage(ENUM_ERROR_LEVEL level)` - 移除第一個訊息（移除並返回副本）

**統計方法**:

- `int GetMessageCount() const` - 獲取訊息總數
- `bool IsEmpty() const` - 檢查是否為空
- `int GetMessageCountByLevel(ENUM_ERROR_LEVEL level) const` - 獲取指定級別的訊息數量
- `bool HasMessages(ENUM_ERROR_LEVEL level) const` - 檢查是否有指定級別的訊息
- `string GetMessageSummary() const` - 獲取所有訊息的摘要統計

**⚠️ 重要提醒**: 所有返回 `TradingMessageRecord*` 的方法都返回副本，調用方必須負責刪除返回的指針以避免內存洩漏。

#### 14. TradingMessageRecord 訊息記錄類

**檔案**: `TradingMessageHandler.mqh` (內部類)
**職責**: 存儲單個訊息記錄的詳細信息

**成員變數**:

- `string m_message` - 訊息內容
- `string m_source` - 訊息來源
- `ENUM_ERROR_LEVEL m_errorLevel` - 訊息級別
- `datetime m_timestamp` - 時間戳

**方法列表**:

- `TradingMessageRecord(string message, string source, ENUM_ERROR_LEVEL errorLevel)` - 構造函數，初始化訊息記錄
- `TradingMessageRecord(const TradingMessageRecord& other)` - 複製構造函數

#### 15. TradingMessageVisitor 訊息訪問者抽象類

**檔案**: `TradingMessageHandler.mqh` (內部類)
**職責**: 實現訪問者模式的抽象基類，用於靈活的訊息處理

**方法列表**:

- `TradingMessageVisitor()` - 構造函數
- `virtual ~TradingMessageVisitor()` - 析構函數
- `virtual void Visit(const TradingMessageRecord& record) = 0` - 抽象方法，訪問訊息記錄

#### 16. 內建訊息訪問者實現

**LogMessageVisitor**: 日誌記錄訪問者，將訊息輸出到日誌
**MessageStatisticsVisitor**: 訊息統計訪問者，收集各級別訊息統計
**MessageFilterVisitor**: 訊息過濾訪問者，只處理特定級別的訊息

### 📦 對象註冊模組

#### 17. ObjectRegistry 對象註冊器

**檔案**: `ObjectRegistry.mqh`
**職責**: 統一對象註冊器，以 HashMap 作為核心成員，提供對象管理功能

**方法列表**:

- `ObjectRegistry(string name, string type, bool owned)` - 構造函數，初始化註冊器
- `virtual ~ObjectRegistry()` - 析構函數，清理資源
- `virtual bool Register(const string key, void* object, const string name, const string description, const string type)` - 核心方法：註冊對象
- `virtual bool Unregister(const string key)` - 核心方法：移除對象
- `virtual void* GetObject(const string key)` - 獲取對象指針
- `virtual ObjectDetail* GetObjectDetail(const string key)` - 獲取對象詳細信息
- `virtual bool Contains(const string key)` - 檢查是否包含指定鍵
- `virtual int GetCount()` - 獲取對象數量
- `virtual void Clear()` - 清空所有對象
- `virtual int GetAllKeys(string &keys[])` - 獲取所有鍵
- `virtual string GetName()` - 獲取註冊器名稱
- `virtual string GetType()` - 獲取註冊器類型
- `virtual bool IsEmpty()` - 檢查是否為空
- `virtual string GetLastRegisteredKey()` - 獲取最後註冊的鍵
- `virtual bool IsOwned()` - 檢查是否擁有對象所有權
- `virtual void SetName(const string name)` - 設置註冊器名稱
- `virtual void SetType(const string type)` - 設置註冊器類型
- `virtual bool UpdateObject(const string key, void* object)` - 更新對象
- `virtual bool RegisterOrUpdate(const string key, void* object, const string name, const string description, const string type)` - 註冊或更新對象
- `virtual HashMap<string, ObjectDetail*>* GetHashMap()` - 獲取 HashMap 實例
- `virtual ObjectResult* GetLastResult()` - 獲取最後操作結果

#### 18. ObjectDetail 對象詳細信息類

**檔案**: `ObjectRegistry.mqh` (內部類)
**職責**: 作為 HashMap 的 Value，存儲對象的詳細信息

**方法列表**:

- `ObjectDetail(const string key, const string name, const string description, void* object, const string type)` - 構造函數
- `string GetKey() const` - 獲取對象鍵
- `string GetName() const` - 獲取對象名稱
- `string GetDescription() const` - 獲取對象描述
- `void* GetObject() const` - 獲取對象指針
- `datetime GetCreateTime() const` - 獲取創建時間
- `datetime GetUpdateTime() const` - 獲取更新時間
- `string GetType() const` - 獲取對象類型
- `void SetName(const string name)` - 設置對象名稱
- `void SetDescription(const string description)` - 設置對象描述
- `void SetType(const string type)` - 設置對象類型
- `void UpdateObject(void* object)` - 更新對象指針
- `string ToString() const` - 轉換為字符串（用於調試）

#### 19. ObjectResult 對象操作結果類

**檔案**: `ObjectRegistry.mqh` (內部類)
**職責**: 存儲對象操作的結果信息

**方法列表**:

- `ObjectResult(bool success, const string message, const string key, const string source)` - 構造函數
- `bool IsSuccess() const` - 檢查是否成功
- `string GetMessage() const` - 獲取消息
- `string GetKey() const` - 獲取鍵
- `string GetSource() const` - 獲取來源
- `datetime GetTimestamp() const` - 獲取時間戳
- `string ToString() const` - 轉換為字符串

## 📊 模組統計總結

### 模組分類統計

- **🔧 核心介面模組**: 2 個 (ITradingPipeline, ITradingPipelineDriver)
- **🏗️ 核心抽象基類模組**: 4 個 (TradingPipeline, TradingPipelineContainerBase, TradingPipelineDriverBase, MainPipeline)
- **📦 容器管理模組**: 2 個 (TradingPipelineContainer, TradingPipelineContainerManager)
- **🚗 驅動器模組**: 1 個 (TradingPipelineDriver)
- **📋 註冊管理模組**: 2 個 (TradingPipelineRegistry, TradingPipelineExplorer)
- **🎮 控制器模組**: 1 個 (TradingController)
- **⚠️ 訊息處理模組**: 4 個 (TradingMessageHandler, TradingMessageRecord, TradingMessageVisitor, 內建訪問者實現)
- **📦 對象註冊模組**: 3 個 (ObjectRegistry, ObjectDetail, ObjectResult)

### 總計

- **主要模組**: 19 個
- **總方法數**: 約 255+ 個方法
- **設計模式**: 7 種 (組合模式、模板方法模式、單例模式、控制器模式、訪問者模式、註冊器模式、裝飾者模式)

### 核心特性

1. **完整的中文註解**: 每個方法都有詳細的中文說明和用途描述
2. **模組化設計**: 遵循 SOLID 原則，職責分離清晰
3. **統一架構**: 合併原有複雜架構，減少約 70% 重複代碼
4. **高效管理**: HashMap 提供 O(1) 查找性能
5. **靈活擴展**: 支持多種設計模式，易於擴展和維護

## 📁 模組結構

```
PipelineAdvance_v1/
├── interface/                                  # 介面定義資料夾
│   ├── ITradingPipeline.mqh                   # 交易流水線介面
│   └── ITradingPipelineDriver.mqh             # 交易流水線驅動器介面
├── docs/                                      # 文檔目錄
├── test/                                      # 測試目錄
├── examples/                                  # 使用示例
├── feature/                                   # 功能擴展目錄
├── TradingPipeline.mqh                        # 抽象基類
├── TradingPipelineContainerBase.mqh           # 容器抽象基類
├── TradingPipelineContainer.mqh               # 容器實現類
├── TradingPipelineContainerManager.mqh        # 容器管理器
├── TradingPipelineDriverBase.mqh              # 驅動器抽象基類
├── TradingPipelineDriver.mqh                  # 驅動器實現類
├── TradingPipelineRegistry.mqh                # 註冊器
├── TradingPipelineExplorer.mqh                # 探索器
├── TradingController.mqh                      # EA 生命週期控制器
├── TradingMessageHandler.mqh                  # 訊息處理器
└── ObjectRegistry.mqh                         # 對象註冊器
```

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義基本流水線操作
- **ITradingPipelineDriver**: 驅動器介面，定義驅動器核心功能
- **TradingPipeline**: 抽象基類，實現基本流水線功能
- **TradingPipelineContainer**: 統一容器類，支持組合模式
- **TradingPipelineDriver**: 單例驅動器，系統核心控制器
- **TradingController**: EA 生命週期控制器
- **TradingMessageHandler**: 統一訊息處理器

## 簡化架構圖

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute()
        +GetName()
        +IsExecuted()
        +Restore()
    }

    class ITradingPipelineDriver {
        <<interface>>
        +Initialize()
        +GetManager()
        +GetRegistry()
        +GetExplorer()
        +GetMessageHandler()
        +Cleanup()
    }

    %% 核心抽象基類
    class TradingPipeline {
        <<abstract>>
        +Execute()
        +GetName()
        +GetStage()
        #Main()*
    }

    class TradingPipelineContainerBase {
        <<abstract>>
        +AddPipeline()
        +Execute()
        +GetPipelineCount()
        #ExecuteInternal()*
        #PreExecuteCheck()
        #PostExecuteProcess()
    }

    class TradingPipelineDriverBase {
        <<abstract>>
        +Initialize()
        +GetManager()
        +GetRegistry()
        #InitializeComponents()*
        #SetupConfiguration()*
    }

    %% 具體實現類
    class TradingPipelineContainer {
        +AddPipeline()
        +Execute()
        +GetPipelineCount()
    }

    class TradingPipelineDriver {
        <<singleton>>
        +GetInstance()$
        +Initialize()
        +GetManager()
        +GetRegistry()
    }

    class TradingController {
        +OnInit()
        +OnTick()
        +OnDeinit()
    }

    class TradingMessageHandler {
        +AddMessage()
        +HandleMessage()
        +GetMessageCount()
        +ClearMessages()
        +GetLastMessage()
        +GetFirstMessage()
        +IsEmpty()
        +PopMessage()
        +ShiftMessage()
        +GetMessageSummary()
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline
    ITradingPipeline <|.. TradingPipelineContainerBase
    ITradingPipelineDriver <|.. TradingPipelineDriverBase
    TradingPipelineContainerBase <|-- TradingPipelineContainer
    TradingPipelineDriverBase <|-- TradingPipelineDriver
    TradingPipelineContainer o-- ITradingPipeline
    TradingPipeline --> ITradingPipelineDriver
    TradingController --> TradingPipelineDriver
    TradingPipelineDriver --> TradingMessageHandler
```

### 2. 管理和支援組件

```mermaid
classDiagram
    class TradingPipelineContainerManager {
        +SetContainer()
        +GetContainer()
        +ExecuteAll()
        +GetContainerCount()
    }

    class TradingPipelineRegistry {
        +Register()
        +Unregister()
        +IsStageRegistered()
        +GetRegisteredStageContainer()
    }

    class TradingPipelineExplorer {
        +GetPipeline()
        +GetAllPipelinesByStage()
        +HasPipelineForStage()
        +GenerateExplorationReport()
    }

    class ObjectRegistry {
        +Register()
        +Unregister()
        +GetObject()
        +Contains()
    }

    %% 關係
    TradingPipelineDriver --> TradingPipelineContainerManager
    TradingPipelineDriver --> TradingPipelineRegistry
    TradingPipelineDriver --> TradingPipelineExplorer
    TradingPipelineDriver --> ObjectRegistry
    TradingPipelineExplorer --> TradingPipelineRegistry
    TradingPipelineRegistry --> TradingPipelineContainerManager
```

### 3. 枚舉和結果類型

```mermaid
classDiagram
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        DEINIT_CLEANUP
        ...
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class PipelineResult {
        +IsSuccess()
        +GetMessage()
        +GetTimestamp()
        +ToString()
    }

    class TradingMessageRecord {
        +m_message
        +m_source
        +m_errorLevel
        +m_timestamp
    }

    class TradingMessageVisitor {
        <<abstract>>
        +Visit()*
    }

    %% 關係
    PipelineResult --> ENUM_ERROR_LEVEL
    TradingMessageRecord --> ENUM_ERROR_LEVEL
    TradingMessageHandler --> TradingMessageVisitor
    TradingMessageHandler --> TradingMessageRecord
```

### 4. 完整系統架構

```mermaid
graph TB
    subgraph "EA 層"
        TC[TradingController]
    end

    subgraph "驅動器層"
        TPD[TradingPipelineDriver<br/><<singleton>>]
        TMH[TradingMessageHandler]
        OR[ObjectRegistry]
    end

    subgraph "管理層"
        TPCM[TradingPipelineContainerManager]
        TPR[TradingPipelineRegistry]
        TPE[TradingPipelineExplorer]
    end

    subgraph "抽象基類層"
        TPCB[TradingPipelineContainerBase<br/><<abstract>>]
        TPDB[TradingPipelineDriverBase<br/><<abstract>>]
    end

    subgraph "容器層"
        TPC[TradingPipelineContainer]
        EP[EventPipeline]
        SP[StagePipeline]
    end

    subgraph "流水線層"
        TP[TradingPipeline<br/><<abstract>>]
        MP[MainPipeline<br/><<abstract>>]
        CP[CustomPipeline]
    end

    %% 關係
    TC --> TPD
    TPD --> TPCM
    TPD --> TPR
    TPD --> TPE
    TPD --> TMH
    TPD --> OR

    %% 抽象基類關係
    TPDB --> TPD
    TPCB --> TPC

    TPCM --> TPC
    TPR --> TPC
    TPE --> TPR

    TPC --> TP
    EP --> TPC
    SP --> TPC

    TP --> TPD
    MP --> TP
    CP --> TP
```

## 設計模式

### 核心設計模式

1. **組合模式 (Composite Pattern)**

   - TradingPipelineContainer 可包含多個子流水線
   - 統一處理單個流水線和容器流水線

2. **模板方法模式 (Template Method Pattern)**

   - TradingPipeline 定義執行流程骨架，子類實現具體的 Main() 方法
   - TradingPipelineContainerBase 定義容器執行模板，子類實現 ExecuteInternal() 方法
   - TradingPipelineDriverBase 定義驅動器初始化模板，子類實現 InitializeComponents() 和 SetupConfiguration() 方法

3. **單例模式 (Singleton Pattern)**

   - TradingPipelineDriver 確保全局唯一實例
   - 統一管理整個流水線系統

4. **控制器模式 (Controller Pattern)**

   - TradingController 作為 EA 生命週期統一入口
   - 協調 OnInit、OnTick、OnDeinit 方法

5. **訪問者模式 (Visitor Pattern)**

   - TradingMessageHandler 實現靈活訊息處理
   - 支持動態擴展訊息處理策略

6. **註冊器模式 (Registry Pattern)**

   - TradingPipelineRegistry 管理流水線註冊
   - ObjectRegistry 提供對象管理功能

7. **裝飾者模式 (Decorator Pattern)**
   - LoggerDecorator 為組件添加日誌功能
   - 透明功能擴展，遵循開放/封閉原則

## 核心特性

### 主要優勢

1. **統一架構**

   - 合併 CompositePipeline 和 PipelineGroup 功能
   - 減少約 70% 重複代碼
   - 簡化架構層次

2. **高效管理**

   - HashMap 提供 O(1) 查找性能
   - 靈活的容器數量管理
   - 直觀的 API 設計

3. **階段化處理**

   - 明確的交易階段定義
   - 支持階段化流水線執行
   - 事件驅動的處理模式

4. **訊息處理**

   - 統一的訊息級別定義
   - 訪問者模式實現靈活處理
   - 完整的訊息生命週期管理

5. **EA 生命週期**

   - TradingController 統一入口
   - 自動錯誤處理和狀態追蹤
   - 與流水線系統無縫集成

6. **對象管理**
   - ObjectRegistry 統一對象註冊
   - 靈活的所有權控制
   - 詳細的操作結果反饋

## 使用流程

### 推薦流程（使用 TradingController）

```mql4
// 1. 獲取驅動器實例
TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

// 2. 創建控制器
TradingController* controller = new TradingController(driver);

// 3. 註冊流水線
TradingPipelineRegistry* registry = driver.GetRegistry();
registry.Register(new MyInitPipeline());
registry.Register(new MyTickPipeline());

// 4. EA 生命週期
ENUM_INIT_RETCODE OnInit() { return controller.OnInit(); }
void OnTick() { controller.OnTick(); }
void OnDeinit(int reason) { controller.OnDeinit(reason); }
```

### 基本流程（直接使用驅動器）

```mql4
// 1. 獲取驅動器和組件
TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
TradingPipelineRegistry* registry = driver.GetRegistry();
TradingPipelineContainerManager* manager = driver.GetManager();

// 2. 創建和註冊流水線
TradingPipeline* pipeline = new MyTradingPipeline();
registry.Register(pipeline);

// 3. 執行流水線
manager.ExecuteAll();

// 4. 檢查結果
PipelineResult* result = pipeline.GetResult();
if(result.IsSuccess()) {
    Print("執行成功: ", result.GetMessage());
}
```

## 架構變化總結

### 舊架構 → 新架構

```
舊: PipelineGroupManager → PipelineGroup → CompositePipeline → ITradingPipeline
新: TradingController → TradingPipelineDriver(Base) → TradingPipelineContainer(Base) → ITradingPipeline
```

### 主要改進

- ✅ **統一架構**: 合併功能，減少 70% 重複代碼
- ✅ **高效管理**: HashMap 提供 O(1) 查找性能
- ✅ **簡化設計**: 從 4 層減少到 2 層結構
- ✅ **EA 生命週期**: TradingController 統一入口點
- 🆕 **抽象基類**: TradingPipelineContainerBase 和 TradingPipelineDriverBase 提供模板方法模式
- 🆕 **訊息處理**: TradingMessageHandler 統一訊息管理
- 🆕 **對象管理**: ObjectRegistry 統一對象註冊
- 🆕 **介面抽象**: 專門的 interface/ 資料夾

## 總結

PipelineAdvance_v1 提供了一個簡潔、高效、易用的交易流水線架構，通過統一的設計模式和清晰的職責分離，大幅簡化了原有的複雜架構，同時保持了所有核心功能並增強了系統的可維護性和可擴展性。

## 📝 更新記錄

### 最新更新 (基於 git status 更動)

**更新日期**: 2024 年 12 月

**主要更動**:

1. **MainPipeline 類增強**:

   - 構造函數參數順序調整：`stage` 參數移至第一位
   - 新增對象管理功能：`GetMessageHandler()`, `GetObjectDetail()`, `Register()`, `Unregister()`
   - 新增宏定義：`PipelineObjectIsRegistered()`, `GetPipelineObject()`
   - 增強錯誤處理和對象註冊集成

2. **TradingPipelineContainerManager 簡化**:

   - `Execute()` 方法改為返回 `bool` 類型，直接反映執行結果
   - 移除 `IsExecuted()` 方法，簡化狀態管理
   - 移除全局執行狀態追蹤，改為基於單個容器的執行狀態

3. **TradingPipelineDriver 配置增強**:

   - `SetupDefaultConfiguration()` 新增更多預設階段容器
   - 新增 `RegisterErrorHandlerToObjectRegistry()` 方法
   - 增強錯誤處理器和對象註冊器的集成

4. **TradingController 執行優化**:

   - 簡化執行方法，直接返回管理器執行結果
   - `ExecuteTickPipeline()` 新增重置邏輯，確保每次 tick 的狀態一致性

5. **測試更新**:
   - 更新測試以適應新的構造函數參數順序
   - 移除對已刪除方法的測試調用

**影響範圍**:

- 總方法數從約 250+ 增加到約 255+
- 增強了模組間的集成度和錯誤處理能力
- 簡化了執行狀態管理邏輯
- 提高了對象管理的便利性
