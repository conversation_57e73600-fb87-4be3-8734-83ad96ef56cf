#property strict

#include "TradingPipeline.mqh"
#include "TradingPipelineDriver.mqh"

class MainPipeline : public TradingPipeline
{
public:
    MainPipeline(ENUM_TRADING_STAGE stage = INIT_START,
                      string name = "",
                      string type = "MainPipeline",
                      ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, type, stage, driver!=NULL?driver:TradingPipelineDriver::GetInstance())
    {
        
        if(m_driver != NULL)
        {
            TradingPipelineRegistry* registry = m_driver.GetRegistry();
            TradingMessageHandler* errorHandler = m_driver.GetErrorHandler();
            if(registry != NULL)
            {
                registry.Register(GetPointer(this));
                errorHandler.HandlePipelineResult(registry.GetResult());
                errorHandler.GetLastMessage(new LogMessageVisitor());
            }
        }
        
    }

protected:
    // 主程序 - 子類必須實現
    virtual void Main() = 0;

    // 獲取錯誤處理器
    TradingMessageHandler* GetMessageHandler()
    {
        if(m_driver == NULL)
        {
            return NULL;
        }

        return m_driver.GetErrorHandler();
    }

    // 獲取對象詳細信息
    ObjectDetail* GetObjectDetail(string name)
    {
        ObjectRegistry* registry = m_driver.GetObjectRegistry();

        if(
            m_driver == NULL ||
            registry == NULL
          )
        {
            return new ObjectDetail("", "", "", NULL, "");
        }

         return registry.GetObjectDetail(name);
    }

    // 註冊對象到 ObjectRegistry
    bool Register(void* object, string name, string description)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Register(name, object, name, description);
    }

    // 從 ObjectRegistry 移除對象
    bool Unregister(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Unregister(name);
    }

    // 檢查對象是否已註冊
    bool HasObject(string name)
    {
        if(m_driver == NULL)
        {
            return false;
        }

        ObjectRegistry* registry = m_driver.GetObjectRegistry();
        if(registry == NULL)
        {
            return false;
        }

        return registry.Contains(name);
    }

    // 註冊不同類型的值
    bool Register(long value, string name, string description);
    bool Register(double value, string name, string description);
    bool Register(string value, string name, string description);
};

#define PipelineObjectIsRegistered(ObjectType, name) dynamic_cast<ObjectType>(GetObjectDetail(name).GetObject()) == true
#define GetPipelineObject(ObjectType, name) dynamic_cast<ObjectType>(GetObjectDetail(name).GetObject())