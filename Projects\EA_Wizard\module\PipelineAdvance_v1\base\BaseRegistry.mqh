//+------------------------------------------------------------------+
//|                                                  BaseRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingEvent.mqh"
#include "../TradingPipeline.mqh"
#include "../../mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 註冊器模板抽象基類                                               |
//| 實現所有註冊器的共同功能，具體註冊邏輯由子類實現                 |
//+------------------------------------------------------------------+
template<typename Key, typename Val>
class BaseRegistry
{
protected:
    string m_name;                      // 註冊器名稱
    string m_type;                      // 註冊器類型
    int m_maxRegistrations;             // 最大註冊數量
    bool m_isEnabled;                   // 是否啟用
    bool m_owned;                       // 是否擁有註冊項目
    PipelineResult* m_last_result;      // 執行結果
    HashMap<Key, Val> m_registeredItems; // 核心存儲結構

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    BaseRegistry(string name = "BaseRegistry",
                string type = "Registry",
                int maxRegistrations = 50,
                bool owned = true)
        : m_name(name),
          m_type(type),
          m_maxRegistrations(maxRegistrations),
          m_isEnabled(true),
          m_owned(owned),
          m_registeredItems(NULL, false),
          m_last_result(new PipelineResult(false, "註冊器尚未執行操作", name, ERROR_LEVEL_INFO))
    {
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~BaseRegistry()
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 模板方法實現                                                     |
    //+------------------------------------------------------------------+

    // 註冊 Key-Value 對
    virtual bool Register(Key key, Val value)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, "無效的鍵值", ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵已經註冊", ERROR_LEVEL_WARNING);
            return false;
        }

        // 註冊項目
        m_registeredItems.set(key, value);

        UpdateResult(true, "成功註冊項目", ERROR_LEVEL_INFO);
        return true;
    }

    // 獲取已註冊的值
    virtual Val GetRegisteredValue(Key key, Val defaultValue)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊", ERROR_LEVEL_WARNING);
            return defaultValue;
        }

        Val value = m_registeredItems.get(key, defaultValue);
        UpdateResult(true, "成功獲取值", ERROR_LEVEL_INFO);
        return value;
    }

    // 更新已註冊的值
    virtual bool UpdateRegisteredValue(Key key, Val newValue)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已註冊
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊，無法更新", ERROR_LEVEL_ERROR);
            return false;
        }

        // 更新值
        m_registeredItems.set(key, newValue);

        UpdateResult(true, "成功更新值", ERROR_LEVEL_INFO);
        return true;
    }

    // 檢查指定鍵是否已註冊
    virtual bool IsRegistered(Key key)
    {
        return m_registeredItems.contains(key);
    }

    // 取消註冊指定鍵
    virtual bool Unregister(Key key)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, "鍵未註冊，無法取消註冊", ERROR_LEVEL_WARNING);
            return false;
        }

        bool result = m_registeredItems.remove(key);
        if(result)
        {
            UpdateResult(true, "成功取消註冊鍵", ERROR_LEVEL_INFO);
        }
        else
        {
            UpdateResult(false, "取消註冊失敗", ERROR_LEVEL_ERROR);
        }

        return result;
    }

    // 清理所有註冊項目
    virtual void Clear()
    {
        int totalCleared = GetRegisteredCount();
        m_registeredItems.clear();

        UpdateResult(true, StringFormat("已清理 %d 個註冊項目", totalCleared), ERROR_LEVEL_INFO);
    }

    // 獲取已註冊項目數量
    virtual int GetRegisteredCount()
    {
        return m_registeredItems.size();
    }

    //+------------------------------------------------------------------+
    //| 狀態查詢實現                                                     |
    //+------------------------------------------------------------------+

    // 獲取最大註冊數量
    virtual int GetMaxRegistrations()
    {
        return m_maxRegistrations;
    }

    // 檢查是否已滿
    virtual bool IsFull()
    {
        return GetRegisteredCount() >= m_maxRegistrations;
    }

    // 檢查是否為空
    virtual bool IsEmpty()
    {
        return GetRegisteredCount() == 0;
    }

    //+------------------------------------------------------------------+
    //| 狀態管理實現                                                     |
    //+------------------------------------------------------------------+

    // 設置啟用狀態
    virtual void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
        UpdateResult(true, StringFormat("註冊器狀態已設置為: %s", enabled ? "啟用" : "禁用"), ERROR_LEVEL_INFO);
    }

    // 檢查是否啟用
    virtual bool IsEnabled()
    {
        return m_isEnabled;
    }

    // 設置擁有狀態
    virtual void SetOwned(bool owned)
    {
        m_owned = owned;
        UpdateResult(true, StringFormat("擁有狀態已設置為: %s", owned ? "是" : "否"), ERROR_LEVEL_INFO);
    }

    // 檢查是否擁有註冊項目
    virtual bool IsOwned()
    {
        return m_owned;
    }

    //+------------------------------------------------------------------+
    //| 結果和信息實現                                                   |
    //+------------------------------------------------------------------+

    // 獲取執行結果
    virtual PipelineResult* GetResult()
    {
        return m_last_result;
    }

    // 獲取註冊器名稱
    virtual string GetName()
    {
        return m_name;
    }

    // 獲取註冊器類型
    virtual string GetType()
    {
        return m_type;
    }

    // 獲取註冊器狀態信息
    virtual string GetStatusInfo()
    {
        string info = StringFormat(
            "註冊器名稱: %s\n"
            "類型: %s\n"
            "狀態: %s\n"
            "擁有項目: %s\n"
            "已註冊數: %d\n"
            "最大註冊數: %d\n"
            "使用率: %.1f%%",
            m_name,
            m_type,
            m_isEnabled ? "啟用" : "禁用",
            m_owned ? "是" : "否",
            GetRegisteredCount(),
            m_maxRegistrations,
            (m_maxRegistrations > 0) ? (GetRegisteredCount() * 100.0 / m_maxRegistrations) : 0.0
        );
        return info;
    }

protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+

    // 更新執行結果
    void UpdateResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        if(m_last_result != NULL)
        {
            delete m_last_result;
        }
        m_last_result = new PipelineResult(success, message, GetName(), errorLevel);
    }

    // 檢查註冊器是否可用
    bool IsRegistryAvailable()
    {
        if(!m_isEnabled)
        {
            UpdateResult(false, "註冊器已禁用，無法執行操作", ERROR_LEVEL_WARNING);
            return false;
        }
        return true;
    }

    // 檢查是否已達到最大註冊數量
    bool CheckMaxRegistrations()
    {
        if(GetRegisteredCount() >= m_maxRegistrations)
        {
            UpdateResult(false, StringFormat("已達到最大註冊數量 %d", m_maxRegistrations), ERROR_LEVEL_ERROR);
            return false;
        }
        return true;
    }

    // 驗證鍵值
    virtual bool ValidateKey(Key key)
    {
        // 基本鍵值驗證，子類可以重寫
        return true;
    }

    // 獲取所有已註冊的鍵
    virtual int GetAllKeys(Key &keys[])
    {
        int count = 0;
        ArrayResize(keys, GetRegisteredCount());

        foreachm(Key, key, Val, value, m_registeredItems)
        {
            keys[count] = key;
            count++;
        }

        return count;
    }
};
