//+------------------------------------------------------------------+
//|                                        TestNewRegistryMembers.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineDriverBase.mqh"
#include "../LongRegistry.mqh"
#include "../DoubleRegistry.mqh"
#include "../StringRegistry.mqh"

//+------------------------------------------------------------------+
//| 測試實現類別                                                     |
//+------------------------------------------------------------------+
class TestDriverImpl : public TradingPipelineDriverBase
{
public:
    TestDriverImpl() : TradingPipelineDriverBase("TestDriver", "TestType") {}
    virtual ~TestDriverImpl() {}

protected:
    virtual bool InitializeComponents() override
    {
        // 初始化核心組件
        m_manager = new TradingPipelineContainerManager("TestManager");
        m_registry = new TradingPipelineRegistry(m_manager, "TestRegistry");
        m_explorer = new TradingPipelineExplorer(m_registry, "TestExplorer");
        m_objectRegistry = new ObjectRegistry("TestObjectRegistry", "TestType", false);
        m_errorHandler = new TradingMessageHandler();

        // 初始化新的類型特定註冊器
        m_longRegistry = new LongRegistry("TestLongRegistry", "LongType", 50, true);
        m_doubleRegistry = new DoubleRegistry("TestDoubleRegistry", "DoubleType", 50, true, 0.00001);
        m_stringRegistry = new StringRegistry("TestStringRegistry", "StringType", 50, true, true, 1024);

        return true;
    }

    virtual bool SetupConfiguration() override
    {
        return true;
    }
};

//+------------------------------------------------------------------+
//| 測試新增的 Registry 成員                                        |
//+------------------------------------------------------------------+
void TestNewRegistryMembers()
{
    Print("=== 開始測試新增的 Registry 成員 ===");

    TestDriverImpl* driver = new TestDriverImpl();
    
    // 測試初始化
    bool initResult = driver.Initialize();
    Print("初始化結果: ", initResult ? "成功" : "失敗");
    
    if(!initResult)
    {
        Print("❌ 初始化失敗，測試終止");
        delete driver;
        return;
    }

    // 測試 IsInitialized 包含新成員檢查
    bool isInitialized = driver.IsInitialized();
    Print("完整初始化檢查: ", isInitialized ? "通過" : "失敗");

    // 測試 getter 方法
    LongRegistry* longReg = driver.GetLongRegistry();
    DoubleRegistry* doubleReg = driver.GetDoubleRegistry();
    StringRegistry* stringReg = driver.GetStringRegistry();

    Print("Long Registry 獲取: ", longReg != NULL ? "成功" : "失敗");
    Print("Double Registry 獲取: ", doubleReg != NULL ? "成功" : "失敗");
    Print("String Registry 獲取: ", stringReg != NULL ? "成功" : "失敗");

    // 測試 Registry 功能
    if(longReg != NULL)
    {
        bool longRegResult = longReg.Register("test_key", 12345);
        long retrievedLong = longReg.GetRegisteredValue("test_key", 0);
        Print("Long Registry 功能測試: ", (longRegResult && retrievedLong == 12345) ? "通過" : "失敗");
        Print("  - 註冊結果: ", longRegResult ? "成功" : "失敗");
        Print("  - 獲取值: ", retrievedLong);
    }

    if(doubleReg != NULL)
    {
        bool doubleRegResult = doubleReg.Register("test_key", 123.456);
        double retrievedDouble = doubleReg.GetRegisteredValue("test_key", 0.0);
        Print("Double Registry 功能測試: ", (doubleRegResult && MathAbs(retrievedDouble - 123.456) < 0.001) ? "通過" : "失敗");
        Print("  - 註冊結果: ", doubleRegResult ? "成功" : "失敗");
        Print("  - 獲取值: ", retrievedDouble);
    }

    if(stringReg != NULL)
    {
        bool stringRegResult = stringReg.Register("test_key", "Hello World");
        string retrievedString = stringReg.GetRegisteredValue("test_key", "");
        Print("String Registry 功能測試: ", (stringRegResult && retrievedString == "Hello World") ? "通過" : "失敗");
        Print("  - 註冊結果: ", stringRegResult ? "成功" : "失敗");
        Print("  - 獲取值: ", retrievedString);
    }

    // 測試統計方法
    int longCount = driver.GetTotalLongRegistrations();
    int doubleCount = driver.GetTotalDoubleRegistrations();
    int stringCount = driver.GetTotalStringRegistrations();

    Print("統計信息:");
    Print("  - Long 註冊數量: ", longCount);
    Print("  - Double 註冊數量: ", doubleCount);
    Print("  - String 註冊數量: ", stringCount);

    // 測試狀態報告
    string statusReport = driver.GetStatusReport();
    bool hasLongInfo = StringFind(statusReport, "Long 註冊器") >= 0;
    bool hasDoubleInfo = StringFind(statusReport, "Double 註冊器") >= 0;
    bool hasStringInfo = StringFind(statusReport, "String 註冊器") >= 0;

    Print("狀態報告包含新 Registry 信息: ", (hasLongInfo && hasDoubleInfo && hasStringInfo) ? "通過" : "失敗");

    // 測試組件驗證
    bool componentsValid = driver.ValidateComponents();
    Print("組件驗證: ", componentsValid ? "通過" : "失敗");

    // 清理
    delete driver;
    
    Print("=== 新增 Registry 成員測試完成 ===");
}

//+------------------------------------------------------------------+
//| 腳本主函數                                                       |
//+------------------------------------------------------------------+
void OnStart()
{
    TestNewRegistryMembers();
}
