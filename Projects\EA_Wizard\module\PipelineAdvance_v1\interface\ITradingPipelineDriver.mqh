#property strict

#include "../TradingEvent.mqh"

//+------------------------------------------------------------------+
//| ITradingPipelineDriver.mqh                                       |
//| 交易流水線驅動器介面定義                                         |
//| 定義了流水線驅動器必須實現的核心管理和控制功能                   |
//+------------------------------------------------------------------+

// 前向聲明，避免循環依賴
class TradingPipelineContainerManager;
class TradingPipelineRegistry;
class TradingPipelineExplorer;
class ObjectRegistry;
class TradingMessageHandler;
class LongRegistry;
class DoubleRegistry;
class StringRegistry;

//+------------------------------------------------------------------+
//| 交易流水線驅動器介面                                             |
//| 遵循介面隔離原則，定義驅動器的核心功能                           |
//| 不包含setter方法，支持不可變設計和單例模式                       |
//+------------------------------------------------------------------+
interface ITradingPipelineDriver
{
    //+------------------------------------------------------------------+
    //| 初始化驅動器                                                     |
    //| 初始化所有核心組件（管理器、註冊器、探索器）                     |
    //| @return bool true表示初始化成功，false表示失敗                   |
    //+------------------------------------------------------------------+
    bool Initialize();

    //+------------------------------------------------------------------+
    //| 獲取容器管理器                                                   |
    //| 返回流水線容器管理器實例，用於管理所有容器                       |
    //| @return TradingPipelineContainerManager* 容器管理器指針          |
    //+------------------------------------------------------------------+
    TradingPipelineContainerManager* GetManager() const;

    //+------------------------------------------------------------------+
    //| 獲取註冊器                                                       |
    //| 返回流水線註冊器實例，用於註冊和管理流水線                       |
    //| @return TradingPipelineRegistry* 註冊器指針                      |
    //+------------------------------------------------------------------+
    TradingPipelineRegistry* GetRegistry() const;

    //+------------------------------------------------------------------+
    //| 獲取探索器                                                       |
    //| 返回流水線探索器實例，用於查詢和探索流水線                       |
    //| @return TradingPipelineExplorer* 探索器指針                      |
    //+------------------------------------------------------------------+
    TradingPipelineExplorer* GetExplorer() const;

    //+------------------------------------------------------------------+
    //| 獲取對象註冊器                                                   |
    //| 返回對象註冊器實例，用於註冊和管理各種對象                       |
    //| @return ObjectRegistry* 對象註冊器指針                           |
    //+------------------------------------------------------------------+
    ObjectRegistry* GetObjectRegistry() const;

    //+------------------------------------------------------------------+
    //| 獲取訊息處理器                                                   |
    //| 返回訊息處理器實例，用於處理和管理系統訊息                       |
    //| @return TradingMessageHandler* 訊息處理器指針                    |
    //+------------------------------------------------------------------+
    TradingMessageHandler* GetErrorHandler() const;

    //+------------------------------------------------------------------+
    //| 獲取 Long 類型註冊器                                            |
    //| 返回 Long 類型註冊器實例，用於註冊和管理 long 類型數據          |
    //| @return LongRegistry* Long 類型註冊器指針                        |
    //+------------------------------------------------------------------+
    LongRegistry* GetLongRegistry() const;

    //+------------------------------------------------------------------+
    //| 獲取 Double 類型註冊器                                          |
    //| 返回 Double 類型註冊器實例，用於註冊和管理 double 類型數據      |
    //| @return DoubleRegistry* Double 類型註冊器指針                    |
    //+------------------------------------------------------------------+
    DoubleRegistry* GetDoubleRegistry() const;

    //+------------------------------------------------------------------+
    //| 獲取 String 類型註冊器                                          |
    //| 返回 String 類型註冊器實例，用於註冊和管理 string 類型數據      |
    //| @return StringRegistry* String 類型註冊器指針                    |
    //+------------------------------------------------------------------+
    StringRegistry* GetStringRegistry() const;

    //+------------------------------------------------------------------+
    //| 檢查初始化狀態                                                   |
    //| 返回驅動器是否已成功初始化                                       |
    //| @return bool true表示已初始化，false表示未初始化                 |
    //+------------------------------------------------------------------+
    bool IsInitialized() const;

    //+------------------------------------------------------------------+
    //| 獲取驅動器名稱                                                   |
    //| 返回驅動器的唯一標識名稱                                         |
    //| @return string 驅動器名稱                                        |
    //+------------------------------------------------------------------+
    string GetName() const;

    //+------------------------------------------------------------------+
    //| 獲取驅動器類型                                                   |
    //| 返回驅動器的類型標識                                             |
    //| @return string 驅動器類型                                        |
    //+------------------------------------------------------------------+
    string GetType() const;

    //+------------------------------------------------------------------+
    //| 設置默認配置                                                     |
    //| 創建和配置默認的流水線容器和組件                                 |
    //| @return bool true表示配置成功，false表示失敗                     |
    //+------------------------------------------------------------------+
    bool SetupDefaultConfiguration();

    //+------------------------------------------------------------------+
    //| 清理驅動器                                                       |
    //| 清理所有組件和資源，準備驅動器關閉                               |
    //+------------------------------------------------------------------+
    void Cleanup();
};
