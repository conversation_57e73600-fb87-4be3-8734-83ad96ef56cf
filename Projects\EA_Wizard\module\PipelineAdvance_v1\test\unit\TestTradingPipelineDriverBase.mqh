#property strict

//+------------------------------------------------------------------+
//| TestTradingPipelineDriverBase.mqh                               |
//| TradingPipelineDriverBase 單元測試                              |
//| 測試驅動器抽象基類的模板方法模式和基本功能                       |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../TradingPipelineDriverBase.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "../../TradingPipelineExplorer.mqh"
#include "../../TradingErrorHandler.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體實現類                                               |
//+------------------------------------------------------------------+
class TestTradingPipelineDriverImpl : public TradingPipelineDriverBase
{
private:
    bool m_initComponentsResult;
    bool m_setupConfigResult;
    bool m_preInitCheckResult;
    bool m_postInitProcessResult;

public:
    TestTradingPipelineDriverImpl(string name = "TestDriver",
                                 string type = "TestDriverType")
        : TradingPipelineDriverBase(name, type),
          m_initComponentsResult(true),
          m_setupConfigResult(true),
          m_preInitCheckResult(true),
          m_postInitProcessResult(true) {}

    virtual ~TestTradingPipelineDriverImpl() {}

    // 設置測試結果
    void SetInitComponentsResult(bool result) { m_initComponentsResult = result; }
    void SetSetupConfigResult(bool result) { m_setupConfigResult = result; }
    void SetPreInitCheckResult(bool result) { m_preInitCheckResult = result; }
    void SetPostInitProcessResult(bool result) { m_postInitProcessResult = result; }

protected:
    // 實現抽象方法
    virtual bool InitializeComponents() override
    {
        if(m_initComponentsResult)
        {
            // 模擬成功初始化組件
            m_manager = new TradingPipelineContainerManager("TestManager");
            m_registry = new TradingPipelineRegistry(m_manager, "TestRegistry");
            m_explorer = new TradingPipelineExplorer(m_registry, "TestExplorer");
            m_objectRegistry = new ObjectRegistry("TestObjectRegistry", "TestType", false);

            // 初始化錯誤處理器（如果基類中為 NULL）
            if(m_errorHandler == NULL)
            {
                m_errorHandler = new TradingMessageHandler();
            }

            // 初始化新的類型特定註冊器
            m_longRegistry = new LongRegistry("TestLongRegistry", "TestLongType", 20, true);
            m_doubleRegistry = new DoubleRegistry("TestDoubleRegistry", "TestDoubleType", 20, true, 0.00001);
            m_stringRegistry = new StringRegistry("TestStringRegistry", "TestStringType", 20, true, true, 512);
        }
        return m_initComponentsResult;
    }

    virtual bool SetupConfiguration() override
    {
        return m_setupConfigResult;
    }

    virtual bool PreInitializeCheck() override
    {
        return m_preInitCheckResult;
    }

    virtual bool PostInitializeProcess() override
    {
        return m_postInitProcessResult;
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineDriverBase 測試類                                |
//+------------------------------------------------------------------+
class TestTradingPipelineDriverBase : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineDriverBase(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineDriverBase"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineDriverBase() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineDriverBase 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestTemplateMethodPattern();
        TestInitializationFlow();
        TestErrorHandling();
        TestComponentAccess();
        TestTypeSpecificRegistries();

        Print("=== TradingPipelineDriverBase 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineDriverBase 構造函數 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("TestDriver", "TestType");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestConstructor - 實例創建",
                driver != NULL,
                driver != NULL ? "驅動器實例創建成功" : "驅動器實例創建失敗"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestConstructor - 名稱設置",
                driver.GetName() == "TestDriver",
                "驅動器名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestConstructor - 類型設置",
                driver.GetType() == "TestType",
                "驅動器類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestConstructor - 初始化狀態",
                !driver.IsInitialized(),
                "驅動器初始狀態為未初始化"
            ));
        }

        delete driver;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineDriverBase 基本屬性 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("PropTest", "PropType");

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestBasicProperties - 獲取名稱",
                driver.GetName() == "PropTest",
                "名稱獲取正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestBasicProperties - 獲取類型",
                driver.GetType() == "PropType",
                "類型獲取正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestBasicProperties - 初始化前組件為空",
                driver.GetManager() == NULL && driver.GetRegistry() == NULL && driver.GetExplorer() == NULL && driver.GetObjectRegistry() == NULL &&
                driver.GetLongRegistry() == NULL && driver.GetDoubleRegistry() == NULL && driver.GetStringRegistry() == NULL,
                "初始化前組件為空"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestBasicProperties - 錯誤處理器初始狀態",
                driver.GetErrorHandler() == NULL,
                "錯誤處理器在構造函數中正確初始化為 NULL"
            ));
        }

        delete driver;
    }

    // 測試模板方法模式
    void TestTemplateMethodPattern()
    {
        Print("--- 測試 TradingPipelineDriverBase 模板方法模式 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("TemplateTest", "TemplateType");

        // 測試成功的初始化流程
        bool initResult = driver.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestTemplateMethodPattern - 成功初始化",
                initResult && driver.IsInitialized(),
                "模板方法執行成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestTemplateMethodPattern - 組件初始化",
                driver.GetManager() != NULL && driver.GetRegistry() != NULL && driver.GetExplorer() != NULL && driver.GetObjectRegistry() != NULL && driver.GetErrorHandler() != NULL &&
                driver.GetLongRegistry() != NULL && driver.GetDoubleRegistry() != NULL && driver.GetStringRegistry() != NULL,
                "組件初始化成功"
            ));
        }

        delete driver;
    }

    // 測試初始化流程
    void TestInitializationFlow()
    {
        Print("--- 測試 TradingPipelineDriverBase 初始化流程 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("FlowTest", "FlowType");

        // 測試前置檢查失敗
        driver.SetPreInitCheckResult(false);
        bool result1 = driver.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestInitializationFlow - 前置檢查失敗",
                !result1 && !driver.IsInitialized(),
                "前置檢查失敗時正確處理"
            ));
        }

        delete driver;

        // 測試組件初始化失敗
        driver = new TestTradingPipelineDriverImpl("FlowTest2", "FlowType2");
        driver.SetInitComponentsResult(false);
        bool result2 = driver.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestInitializationFlow - 組件初始化失敗",
                !result2 && !driver.IsInitialized(),
                "組件初始化失敗時正確處理"
            ));
        }

        delete driver;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("--- 測試 TradingPipelineDriverBase 錯誤處理 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("ErrorTest", "ErrorType");

        // 測試配置設置失敗
        driver.SetSetupConfigResult(false);
        bool result = driver.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestErrorHandling - 配置失敗處理",
                !result && !driver.IsInitialized(),
                "配置失敗時正確處理"
            ));

            PipelineResult* lastResult = driver.GetResult();
            bool hasErrorResult = (lastResult != NULL && !lastResult.IsSuccess());
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestErrorHandling - 錯誤結果記錄",
                hasErrorResult,
                hasErrorResult ? "錯誤結果正確記錄" : "錯誤結果記錄失敗: " + (lastResult == NULL ? "結果為空" : "結果狀態錯誤")
            ));
        }

        delete driver;
    }

    // 測試組件訪問
    void TestComponentAccess()
    {
        Print("--- 測試 TradingPipelineDriverBase 組件訪問 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("AccessTest", "AccessType");
        driver.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - 管理器訪問",
                driver.GetManager() != NULL,
                "管理器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - 註冊器訪問",
                driver.GetRegistry() != NULL,
                "註冊器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - 探索器訪問",
                driver.GetExplorer() != NULL,
                "探索器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - 對象註冊器訪問",
                driver.GetObjectRegistry() != NULL,
                "對象註冊器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - 錯誤處理器訪問",
                driver.GetErrorHandler() != NULL,
                "錯誤處理器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - Long 註冊器訪問",
                driver.GetLongRegistry() != NULL,
                "Long 註冊器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - Double 註冊器訪問",
                driver.GetDoubleRegistry() != NULL,
                "Double 註冊器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestComponentAccess - String 註冊器訪問",
                driver.GetStringRegistry() != NULL,
                "String 註冊器訪問正常"
            ));
        }

        delete driver;
    }

    // 測試類型特定註冊器
    void TestTypeSpecificRegistries()
    {
        Print("--- 測試 TradingPipelineDriverBase 類型特定註冊器 ---");

        TestTradingPipelineDriverImpl* driver = new TestTradingPipelineDriverImpl("RegistryTest", "RegistryType");
        driver.Initialize();

        if(m_runner != NULL)
        {
            // 測試 Long 註冊器功能
            LongRegistry* longRegistry = driver.GetLongRegistry();
            if(longRegistry != NULL)
            {
                bool longRegisterResult = longRegistry.Register("test_long", 12345);
                long retrievedLong = longRegistry.GetRegisteredValue("test_long", 0);

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - Long 註冊器功能",
                    longRegisterResult && retrievedLong == 12345,
                    "Long 註冊器註冊和獲取功能正常"
                ));

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - Long 註冊器統計",
                    driver.GetTotalLongRegistrations() == 1,
                    "Long 註冊器統計正確"
                ));
            }

            // 測試 Double 註冊器功能
            DoubleRegistry* doubleRegistry = driver.GetDoubleRegistry();
            if(doubleRegistry != NULL)
            {
                bool doubleRegisterResult = doubleRegistry.Register("test_double", 123.456);
                double retrievedDouble = doubleRegistry.GetRegisteredValue("test_double", 0.0);

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - Double 註冊器功能",
                    doubleRegisterResult && MathAbs(retrievedDouble - 123.456) < 0.001,
                    "Double 註冊器註冊和獲取功能正常"
                ));

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - Double 註冊器統計",
                    driver.GetTotalDoubleRegistrations() == 1,
                    "Double 註冊器統計正確"
                ));
            }

            // 測試 String 註冊器功能
            StringRegistry* stringRegistry = driver.GetStringRegistry();
            if(stringRegistry != NULL)
            {
                bool stringRegisterResult = stringRegistry.Register("test_string", "Hello World");
                string retrievedString = stringRegistry.GetRegisteredValue("test_string", "");

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - String 註冊器功能",
                    stringRegisterResult && retrievedString == "Hello World",
                    "String 註冊器註冊和獲取功能正常"
                ));

                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - String 註冊器統計",
                    driver.GetTotalStringRegistrations() == 1,
                    "String 註冊器統計正確"
                ));
            }

            // 測試狀態報告包含新註冊器信息
            string statusReport = driver.GetStatusReport();
            bool hasLongInfo = StringFind(statusReport, "Long 註冊器") >= 0;
            bool hasDoubleInfo = StringFind(statusReport, "Double 註冊器") >= 0;
            bool hasStringInfo = StringFind(statusReport, "String 註冊器") >= 0;

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - 狀態報告包含新註冊器",
                hasLongInfo && hasDoubleInfo && hasStringInfo,
                "狀態報告正確包含新註冊器信息"
            ));

            // 測試組件詳細信息包含新註冊器
            string componentInfo = driver.GetComponentInfo();
            bool hasLongComponentInfo = StringFind(componentInfo, "Long 註冊器") >= 0;
            bool hasDoubleComponentInfo = StringFind(componentInfo, "Double 註冊器") >= 0;
            bool hasStringComponentInfo = StringFind(componentInfo, "String 註冊器") >= 0;

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverBase::TestTypeSpecificRegistries - 組件信息包含新註冊器",
                hasLongComponentInfo && hasDoubleComponentInfo && hasStringComponentInfo,
                "組件詳細信息正確包含新註冊器信息"
            ));
        }

        delete driver;
    }
};
