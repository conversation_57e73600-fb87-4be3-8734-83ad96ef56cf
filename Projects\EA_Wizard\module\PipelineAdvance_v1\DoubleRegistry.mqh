//+------------------------------------------------------------------+
//|                                                DoubleRegistry.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "base/BaseRegistry.mqh"

//+------------------------------------------------------------------+
//| Double 類型註冊器                                               |
//| 專門用於管理 double 類型數據的註冊器                            |
//+------------------------------------------------------------------+
class DoubleRegistry : public BaseRegistry<string, double>
{
private:
    double m_precision;                        // 精度控制

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //+------------------------------------------------------------------+
    DoubleRegistry(string name = "DoubleRegistry",
                  string type = "DoubleRegistry",
                  int maxRegistrations = 50,
                  bool owned = true,
                  double precision = 0.00001)
        : BaseRegistry<string, double>(name, type, maxRegistrations, owned),
          m_precision(precision)
    {
        UpdateResult(true, "Double 註冊器構造完成", ERROR_LEVEL_INFO);
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~DoubleRegistry()
    {
        Clear();
    }

    //+------------------------------------------------------------------+
    //| 註冊 double 值                                                  |
    //+------------------------------------------------------------------+
    bool Register(string key, double value)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 檢查是否已達到最大註冊數量
        if(!CheckMaxRegistrations())
        {
            return false;
        }

        // 驗證鍵值
        if(!ValidateKey(key))
        {
            UpdateResult(false, StringFormat("無效的鍵值: '%s'", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 驗證 double 值
        if(!ValidateDoubleValue(value))
        {
            UpdateResult(false, StringFormat("無效的 double 值: %.5f", value), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已經註冊
        if(m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 '%s' 已經註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        // 註冊項目
        m_registeredItems.set(key, value);

        UpdateResult(true, StringFormat("成功註冊 double 值: 鍵='%s', 值=%.5f", key, value), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 獲取已註冊的 double 值                                          |
    //+------------------------------------------------------------------+
    double GetRegisteredValue(string key, double defaultValue = 0.0)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 '%s' 未註冊", key), ERROR_LEVEL_WARNING);
            return defaultValue;
        }

        double value = m_registeredItems.get(key, defaultValue);
        UpdateResult(true, StringFormat("成功獲取 double 值: 鍵='%s', 值=%.5f", key, value), ERROR_LEVEL_INFO);
        return value;
    }

    //+------------------------------------------------------------------+
    //| 更新已註冊的 double 值                                          |
    //+------------------------------------------------------------------+
    bool UpdateRegisteredValue(string key, double newValue)
    {
        // 檢查註冊器是否可用
        if(!IsRegistryAvailable())
        {
            return false;
        }

        // 驗證 double 值
        if(!ValidateDoubleValue(newValue))
        {
            UpdateResult(false, StringFormat("無效的 double 值: %.5f", newValue), ERROR_LEVEL_ERROR);
            return false;
        }

        // 檢查是否已註冊
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 '%s' 未註冊，無法更新", key), ERROR_LEVEL_ERROR);
            return false;
        }

        // 更新值
        m_registeredItems.set(key, newValue);

        UpdateResult(true, StringFormat("成功更新 double 值: 鍵='%s', 新值=%.5f", key, newValue), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法                                                     |
    //+------------------------------------------------------------------+

    // 檢查指定鍵是否已註冊
    virtual bool IsRegistered(string key)
    {
        return m_registeredItems.contains(key);
    }

    // 取消註冊指定鍵
    virtual bool Unregister(string key)
    {
        if(!m_registeredItems.contains(key))
        {
            UpdateResult(false, StringFormat("鍵 '%s' 未註冊，無法取消註冊", key), ERROR_LEVEL_WARNING);
            return false;
        }

        bool result = m_registeredItems.remove(key);
        if(result)
        {
            UpdateResult(true, StringFormat("成功取消註冊鍵: '%s'", key), ERROR_LEVEL_INFO);
        }
        else
        {
            UpdateResult(false, StringFormat("取消註冊鍵 '%s' 失敗", key), ERROR_LEVEL_ERROR);
        }

        return result;
    }

    // 清理所有註冊項目
    virtual void Clear()
    {
        int totalCleared = GetRegisteredCount();
        m_registeredItems.clear();

        UpdateResult(true, StringFormat("已清理 %d 個 double 註冊項目", totalCleared), ERROR_LEVEL_INFO);
    }

    // 獲取已註冊項目數量
    virtual int GetRegisteredCount()
    {
        return m_registeredItems.size();
    }

    //+------------------------------------------------------------------+
    //| 專用方法                                                         |
    //+------------------------------------------------------------------+

    // 設置精度
    void SetPrecision(double precision)
    {
        m_precision = MathMax(precision, 0.00001);
        UpdateResult(true, StringFormat("精度已設置為: %.8f", m_precision), ERROR_LEVEL_INFO);
    }

    // 獲取精度
    double GetPrecision()
    {
        return m_precision;
    }

    // 獲取所有已註冊的鍵
    int GetAllKeys(string &keys[])
    {
        int count = 0;
        ArrayResize(keys, GetRegisteredCount());

        foreachm(string, key, double, value, m_registeredItems)
        {
            keys[count] = key;
            count++;
        }

        return count;
    }

    // 獲取指定範圍內的值
    int GetValuesInRange(double minValue, double maxValue, string &keys[], double &values[])
    {
        int count = 0;
        int totalCount = GetRegisteredCount();

        ArrayResize(keys, totalCount);
        ArrayResize(values, totalCount);

        foreachm(string, key, double, value, m_registeredItems)
        {
            if(value >= minValue && value <= maxValue)
            {
                keys[count] = key;
                values[count] = value;
                count++;
            }
        }

        // 調整數組大小到實際數量
        ArrayResize(keys, count);
        ArrayResize(values, count);

        UpdateResult(true, StringFormat("找到 %d 個在範圍 [%.5f, %.5f] 內的值", count, minValue, maxValue), ERROR_LEVEL_INFO);
        return count;
    }

    // 計算所有註冊值的總和
    double CalculateSum()
    {
        double sum = 0.0;

        foreachm(string, key, double, value, m_registeredItems)
        {
            sum += value;
        }

        UpdateResult(true, StringFormat("計算總和完成: %.5f", sum), ERROR_LEVEL_INFO);
        return sum;
    }

    // 計算平均值
    double CalculateAverage()
    {
        if(IsEmpty())
        {
            UpdateResult(false, "註冊器為空，無法計算平均值", ERROR_LEVEL_WARNING);
            return 0.0;
        }

        double average = CalculateSum() / GetRegisteredCount();
        UpdateResult(true, StringFormat("計算平均值完成: %.5f", average), ERROR_LEVEL_INFO);
        return average;
    }

protected:
    //+------------------------------------------------------------------+
    //| 受保護的輔助方法                                                 |
    //+------------------------------------------------------------------+

    // 驗證 double 值
    bool ValidateDoubleValue(double value)
    {
        // 檢查是否為有效數字
        if(value != value) // NaN 檢查
        {
            return false;
        }

        // 檢查是否為無限大
        if(MathIsValidNumber(value) == false)
        {
            return false;
        }

        return true;
    }

    // 比較兩個 double 值是否相等（考慮精度）
    bool IsDoubleEqual(double value1, double value2)
    {
        return MathAbs(value1 - value2) < m_precision;
    }
};
